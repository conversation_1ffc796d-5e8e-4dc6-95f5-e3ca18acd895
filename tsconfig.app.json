{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    /* 为Figma组件提供路径映射（当需要时取消注释）
    "baseUrl": ".",
    "paths": {
      "@figma-components/*": ["figma-restoration-mcp-vue-tools/src/components/*"],
      "@figma-lib/*": ["figma-restoration-mcp-vue-tools/components/*"]
    },
    */

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]
}
