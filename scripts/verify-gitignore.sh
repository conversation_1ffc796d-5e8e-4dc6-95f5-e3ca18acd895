#!/bin/bash

# 🧪 .gitignore Verification Script
# Tests that .gitignore is properly configured for Vue 3 + Git submodule project

echo "🔍 Verifying .gitignore configuration..."
echo "========================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to test if file/directory is ignored
test_ignored() {
    local file="$1"
    local description="$2"
    
    if git check-ignore "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}: $description ($file is ignored)"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $description ($file is NOT ignored)"
        ((TESTS_FAILED++))
    fi
}

# Function to test if file/directory is tracked
test_tracked() {
    local file="$1"
    local description="$2"
    
    if git check-ignore "$file" >/dev/null 2>&1; then
        echo -e "${RED}❌ FAIL${NC}: $description ($file should NOT be ignored)"
        ((TESTS_FAILED++))
    else
        echo -e "${GREEN}✅ PASS${NC}: $description ($file is tracked)"
        ((TESTS_PASSED++))
    fi
}

echo "📦 Testing Dependencies & Build Outputs..."
test_ignored "node_modules/" "Node modules directory"
test_ignored "dist/" "Distribution directory"
test_ignored "build/" "Build directory"
test_ignored ".vite/" "Vite cache directory"

echo ""
echo "🔧 Testing Development Files..."
test_ignored ".env" "Environment file"
test_ignored ".env.local" "Local environment file"
test_ignored ".DS_Store" "macOS system file"
test_ignored "Thumbs.db" "Windows system file"
test_ignored ".vscode/" "VS Code directory"
test_ignored "*.log" "Log files"

echo ""
echo "🧪 Testing Development Outputs..."
test_ignored "coverage/" "Coverage directory"
test_ignored "screenshots/" "Screenshots directory"
test_ignored "test-results/" "Test results directory"
test_ignored "mcp-vue-tools/" "MCP tools results"

echo ""
echo "💾 Testing Cache & Temporary Files..."
test_ignored "*.tsbuildinfo" "TypeScript build info"
test_ignored ".eslintcache" "ESLint cache"
test_ignored "temp/" "Temporary directory"
test_ignored ".tmp/" "Temporary directory"

echo ""
echo "🎯 Testing Essential Files (Should be tracked)..."
test_tracked "package.json" "Package configuration"
test_tracked "tsconfig.app.json" "TypeScript configuration"
test_tracked "vite.config.ts" "Vite configuration"
test_tracked "src/" "Source directory"
test_tracked "public/" "Public directory"
test_tracked "index.html" "HTML entry point"

echo ""
echo "🔒 Testing Submodule Integrity..."
test_tracked ".gitmodules" "Git submodules configuration"
test_tracked "figma-restoration-mcp-vue-tools/" "Figma tools submodule"

echo ""
echo "========================================"
echo "📊 Test Results Summary:"
echo -e "${GREEN}✅ Passed: $TESTS_PASSED${NC}"
echo -e "${RED}❌ Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! .gitignore is properly configured.${NC}"
    exit 0
else
    echo -e "${RED}⚠️  Some tests failed. Please review .gitignore configuration.${NC}"
    exit 1
fi
