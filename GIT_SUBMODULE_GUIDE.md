# 🔧 Git Submodule Management Guide

## 📋 Overview

This project uses `figma-restoration-mcp-vue-tools` as a Git submodule to maintain clean separation between the main project and Figma component restoration tools.

## 🏗️ Current Configuration

### `.gitmodules` Configuration
```ini
[submodule "figma-restoration-mcp-vue-tools"]
    path = figma-restoration-mcp-vue-tools
    url = https://github.com/tianmuji/figma-restoration-mcp-vue-tools.git
    branch = main
    update = rebase
```

### Submodule Status
- **Repository**: https://github.com/tianmuji/figma-restoration-mcp-vue-tools.git
- **Local Path**: `figma-restoration-mcp-vue-tools/`
- **Tracked Branch**: `main`
- **Update Strategy**: `rebase` (clean, linear history)

## 🚀 Team Collaboration Workflows

### **Initial Setup (New Team Members)**

```bash
# Clone main project with submodules
git clone --recursive <main-project-url>

# OR clone first, then initialize submodules
git clone <main-project-url>
cd <project-directory>
git submodule update --init --recursive
```

### **Daily Development Workflow**

```bash
# Update main project and submodules
git pull
git submodule update --remote --rebase

# Work in submodule (component development)
cd figma-restoration-mcp-vue-tools
npm run dev  # Start testing server on port 83

# Work in main project (component usage)
cd ..
npm run dev  # Start main server on port 5173
```

### **Updating Submodule to Latest Version**

```bash
# Update submodule to latest main branch
cd figma-restoration-mcp-vue-tools
git pull origin main

# Commit the submodule update in main project
cd ..
git add figma-restoration-mcp-vue-tools
git commit -m "chore: update figma-restoration-mcp-vue-tools to latest"
```

### **Making Changes to Submodule**

```bash
# Work in submodule
cd figma-restoration-mcp-vue-tools

# Make changes, test, commit
git add .
git commit -m "feat: add new component"
git push origin main

# Update main project to use new submodule version
cd ..
git add figma-restoration-mcp-vue-tools
git commit -m "chore: update submodule with new component"
```

## 🔍 Verification Commands

### **Check Submodule Status**
```bash
git submodule status
# Expected output: commit-hash figma-restoration-mcp-vue-tools (tag-or-branch)
```

### **Verify Configuration**
```bash
git config -f .gitmodules --list
# Should show submodule configuration
```

### **Check for Uncommitted Changes**
```bash
git status
# Should show clean working tree for production
```

## ⚠️ Common Issues & Solutions

### **Issue: Submodule appears as modified**
```bash
# Check what changed
cd figma-restoration-mcp-vue-tools
git status

# If unintended changes, reset
git checkout .
git clean -fd

# If intended changes, commit them
git add .
git commit -m "description"
```

### **Issue: Submodule not updating**
```bash
# Force update to tracked branch
git submodule update --remote --force
```

### **Issue: Detached HEAD in submodule**
```bash
cd figma-restoration-mcp-vue-tools
git checkout main
git pull origin main
```

## 🎯 Best Practices

1. **Always commit submodule changes** before updating main project
2. **Use descriptive commit messages** for submodule updates
3. **Test both environments** (main project + submodule) before committing
4. **Keep submodule on main branch** for stability
5. **Document any submodule-specific setup** requirements

## 📊 Architecture Benefits

- ✅ **Clean Separation**: Testing tools isolated from main project
- ✅ **Version Control**: Precise submodule version tracking
- ✅ **Team Sync**: Reproducible builds across environments
- ✅ **Independent Development**: Parallel work on main project and tools
- ✅ **Scalability**: Easy to add more component restoration tools

---

**Last Updated**: 2025-07-18  
**Submodule Version**: v1.3.1+  
**Configuration Status**: ✅ Production Ready
