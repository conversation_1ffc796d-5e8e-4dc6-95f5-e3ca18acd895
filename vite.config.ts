import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      // 为Figma组件提供干净的别名（当需要时取消注释）
      // '@figma-components': resolve(__dirname, 'figma-restoration-mcp-vue-tools/src/components'),
      // '@figma-lib': resolve(__dirname, 'figma-restoration-mcp-vue-tools/components')
    }
  }
})
