# 🧪 工作流测试指南

## 📋 当前项目状态

### **主项目** (标准Vue项目)
- **地址**: http://localhost:5174/
- **状态**: ✅ 运行中
- **描述**: 干净的Vue 3 + TypeScript + Vite项目
- **特点**: 
  - 零污染（无测试工具依赖）
  - 标准Vue项目结构
  - 预留Figma组件集成接口

### **Figma工具子模块** (测试环境)
- **地址**: http://localhost:83/
- **路径**: `figma-restoration-mcp-vue-tools/`
- **状态**: ✅ 可用
- **描述**: 完整的Figma组件恢复和测试环境

## 🚀 测试工作流程

### **1. 基础环境验证**

```bash
# 检查主项目
curl http://localhost:5174/  # 应该返回Vue应用

# 检查子模块测试环境
cd figma-restoration-mcp-vue-tools
npm run dev  # 启动测试服务器 (端口83)
```

### **2. Figma组件恢复测试**

```bash
# 在子模块中测试组件
cd figma-restoration-mcp-vue-tools

# 截图测试
snapdom_screenshot_figma-restoration-mcp-vue-tools \
  --componentName DocumentSortMenu \
  --projectPath $(pwd) \
  --port 83

# 精度对比测试
figma_compare_figma-restoration-mcp-vue-tools \
  --componentName DocumentSortMenu \
  --projectPath $(pwd)
```

### **3. 主项目集成测试**

#### **步骤1: 启用Figma组件**

编辑 `src/components/FigmaComponents.ts`，取消注释：
```typescript
// 取消注释这些行：
import DocumentSortMenuComponent from '../figma-restoration-mcp-vue-tools/src/components/DocumentSortMenu/index.vue'
export const DocumentSortMenu = DocumentSortMenuComponent
export type { SortOption, DocumentSortMenuProps } from '../figma-restoration-mcp-vue-tools/src/components/DocumentSortMenu/index.vue'
```

#### **步骤2: 启用路径别名**

编辑 `vite.config.ts`，取消注释：
```typescript
resolve: {
  alias: {
    '@figma-components': resolve(__dirname, 'figma-restoration-mcp-vue-tools/src/components'),
    '@figma-lib': resolve(__dirname, 'figma-restoration-mcp-vue-tools/components')
  }
}
```

编辑 `tsconfig.app.json`，取消注释：
```json
"baseUrl": ".",
"paths": {
  "@figma-components/*": ["figma-restoration-mcp-vue-tools/src/components/*"],
  "@figma-lib/*": ["figma-restoration-mcp-vue-tools/components/*"]
}
```

#### **步骤3: 在主项目中使用组件**

编辑 `src/App.vue`，取消注释Figma组件部分：
```vue
<script setup lang="ts">
import { DocumentSortMenu } from './components/FigmaComponents'
import type { SortOption } from './components/FigmaComponents'

const selectedOption = ref<SortOption>('modifiedTimeDesc')
</script>

<template>
  <!-- 取消注释这个部分 -->
  <div class="figma-components-demo">
    <h2>🎨 Figma恢复的组件</h2>
    <DocumentSortMenu 
      :selected-option="selectedOption"
      @select="(option) => console.log('选择了:', option)"
    />
  </div>
</template>
```

### **4. 验证完整工作流**

#### **预期结果**:
1. ✅ 主项目 (http://localhost:5174/) 显示标准Vue应用
2. ✅ 启用Figma组件后，主项目显示DocumentSortMenu组件
3. ✅ 组件功能正常（点击、选择等）
4. ✅ 子模块测试工具正常工作
5. ✅ 截图和对比功能正常

#### **测试检查点**:
- [ ] 主项目启动无错误
- [ ] 子模块测试环境启动无错误  
- [ ] Figma组件导入无TypeScript错误
- [ ] 组件在主项目中正常渲染
- [ ] 组件交互功能正常
- [ ] 截图工具生成高质量图片
- [ ] 对比工具生成准确度报告

## 🔧 故障排除

### **常见问题**:

1. **端口冲突**: 主项目自动切换到5174端口
2. **TypeScript错误**: 确保路径别名配置正确
3. **组件导入失败**: 检查子模块是否正确初始化
4. **测试工具失败**: 确保在子模块目录中运行

### **重置环境**:
```bash
# 重置主项目
npm run dev

# 重置子模块
cd figma-restoration-mcp-vue-tools
npm run dev
```

## 📊 成功标准

- ✅ **隔离性**: 主项目保持干净，无测试工具污染
- ✅ **功能性**: Figma组件在主项目中正常工作
- ✅ **开发体验**: 热重载、TypeScript支持正常
- ✅ **测试能力**: 截图和对比工具正常工作
- ✅ **可维护性**: 清晰的组件导入和管理方式

---

**准备就绪！** 🎉 您现在可以开始测试完整的Figma组件恢复工作流程了。
