<template>
  <div class="home">
    <div class="header">
      <h1>🎨 Figma Component Restoration</h1>
      <p class="subtitle">高质量Vue组件恢复工具 - 精确还原Figma设计</p>
      <div class="stats">
        <div class="stat-item">
          <span class="stat-number">{{ components.length }}</span>
          <span class="stat-label">组件总数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">99%+</span>
          <span class="stat-label">还原精度</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">3x</span>
          <span class="stat-label">高清截图</span>
        </div>
      </div>
    </div>

    <div class="components-section">
      <div class="section-header">
        <h2>📦 可用组件</h2>
        <div class="view-toggle">
          <button
            :class="{ active: viewMode === 'grid' }"
            @click="viewMode = 'grid'"
            class="toggle-btn"
          >
            <span class="icon">⊞</span> 网格视图
          </button>
          <button
            :class="{ active: viewMode === 'list' }"
            @click="viewMode = 'list'"
            class="toggle-btn"
          >
            <span class="icon">☰</span> 列表视图
          </button>
        </div>
      </div>

      <div :class="['components-container', viewMode]">
        <div v-for="component in components" :key="component" class="component-card">
          <div class="card-header">
            <div class="component-icon">🧩</div>
            <div class="component-info">
              <h3 class="component-name">{{ component }}</h3>
              <div class="component-meta">
                <span class="component-type">{{ getComponentType(component) }}</span>
                <span class="component-status" :class="getStatusClass(component)">
                  {{ getComponentStatus(component) }}
                </span>
              </div>
            </div>
          </div>

          <div class="component-actions">
            <router-link :to="`/component/${component}`" class="action-btn primary">
              <span class="btn-icon">👁</span>
              预览组件
            </router-link>
            <router-link :to="`/report/${component}`" class="action-btn secondary">
              <span class="btn-icon">📊</span>
              查看报告
            </router-link>
          </div>

          <div class="component-tags">
            <span v-if="component.includes('Smart')" class="tag smart">智能版</span>
            <span v-if="component.includes('Optimized')" class="tag optimized">优化版</span>
            <span v-if="component.includes('Ultra')" class="tag ultra">超级版</span>
            <span v-if="component.includes('99')" class="tag perfect">完美版</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      components: [],
      viewMode: 'grid' // 'grid' 或 'list'
    }
  },
  mounted() {
    // 获取所有可用的组件
    this.loadComponents()
  },
  methods: {
    async loadComponents() {
      try {
        // 动态获取所有组件
        const componentModules = import.meta.glob('../components/*/index.vue')
        const componentNames = Object.keys(componentModules).map(path => {
          const match = path.match(/\.\.\/components\/(.+)\/index\.vue$/)
          return match ? match[1] : null
        }).filter(Boolean)

        this.components = componentNames.sort()
      } catch (error) {
        console.error('Failed to load components:', error)
        // 回退到硬编码列表
        this.components = [
          'DesignV1',
          'DesignV2',
          'RedemptionSuccess',
          'ModalRemoveMember',
          'ModalRemoveMember-Smart',
          'ExchangeSuccess',
          'ExchangeSuccess-Optimized',
          'ExchangeSuccess-Ultra',
          'AssignmentComplete',
          'AssignmentComplete-Optimized',
          'AssignmentComplete-Ultra',
          'AssignmentComplete-99',
          'ScanComplete',
          'ScanResult',
          'ExchangeSuccess-Ultra',
          'ExchangeSuccess-Minimal'
        ]
      }
    },

    getComponentType(componentName) {
      if (componentName.includes('Modal')) return '弹窗组件'
      if (componentName.includes('Success')) return '成功页面'
      if (componentName.includes('Complete')) return '完成页面'
      if (componentName.includes('Scan')) return '扫描组件'
      if (componentName.includes('Design')) return '设计组件'
      return '通用组件'
    },

    getComponentStatus(componentName) {
      if (componentName.includes('99')) return '完美还原'
      if (componentName.includes('Ultra')) return '超高精度'
      if (componentName.includes('Optimized')) return '已优化'
      if (componentName.includes('Smart')) return '智能版本'
      return '标准版本'
    },

    getStatusClass(componentName) {
      if (componentName.includes('99')) return 'perfect'
      if (componentName.includes('Ultra')) return 'ultra'
      if (componentName.includes('Optimized')) return 'optimized'
      if (componentName.includes('Smart')) return 'smart'
      return 'standard'
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 40px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0 0 32px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.stats {
  display: flex;
  justify-content: center;
  gap: 48px;
  margin-top: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.components-section {
  padding: 40px 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.section-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.view-toggle {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.toggle-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
}

.toggle-btn .icon {
  font-size: 1rem;
}

.components-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.components-container.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.component-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.component-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.component-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

.list .component-card {
  display: flex;
  align-items: center;
  padding: 20px 24px;
}

.list .component-card .card-header {
  flex: 1;
}

.list .component-card .component-actions {
  margin-top: 0;
  margin-left: 24px;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.component-icon {
  font-size: 2rem;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  flex-shrink: 0;
}

.component-info {
  flex: 1;
}

.component-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.component-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.component-type {
  font-size: 0.85rem;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.component-status {
  font-size: 0.8rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.component-status.perfect {
  background: #dcfce7;
  color: #166534;
}

.component-status.ultra {
  background: #dbeafe;
  color: #1e40af;
}

.component-status.optimized {
  background: #fef3c7;
  color: #92400e;
}

.component-status.smart {
  background: #f3e8ff;
  color: #7c3aed;
}

.component-status.standard {
  background: #f1f5f9;
  color: #475569;
}

.component-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
}

.action-btn .btn-icon {
  font-size: 1rem;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.action-btn.secondary:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.component-tags {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.tag {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag.smart {
  background: #f3e8ff;
  color: #7c3aed;
}

.tag.optimized {
  background: #fef3c7;
  color: #92400e;
}

.tag.ultra {
  background: #dbeafe;
  color: #1e40af;
}

.tag.perfect {
  background: #dcfce7;
  color: #166534;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }

  .stats {
    gap: 24px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .components-container.grid {
    grid-template-columns: 1fr;
  }

  .list .component-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .list .component-card .component-actions {
    margin-left: 0;
    margin-top: 16px;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stats {
    flex-direction: column;
    gap: 16px;
  }

  .component-actions {
    flex-direction: column;
  }

  .view-toggle {
    width: 100%;
    justify-content: center;
  }
}
</style>
