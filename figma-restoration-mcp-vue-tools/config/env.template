# Figma Restoration Kit Environment Configuration
# Copy this file to .env and update the values

# Chrome/Chromium executable path
# macOS: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
# Linux: /usr/bin/google-chrome or /usr/bin/chromium-browser
# Windows: C:\Program Files\Google\Chrome\Application\chrome.exe
PUPPETEER_EXECUTABLE_PATH="{{CHROME_PATH}}"

# Development environment
NODE_ENV=development

# Project root directory (absolute path)
PROJECT_ROOT="{{PROJECT_ROOT}}"

# Figma Personal Access Token (optional)
# Get your token from: https://www.figma.com/developers/api#access-tokens
# FIGMA_PERSONAL_ACCESS_TOKEN=your_token_here

# Vue development server configuration
VUE_DEV_SERVER_PORT=83
VUE_DEV_SERVER_HOST=localhost

# Screenshot configuration
SCREENSHOT_VIEWPORT_WIDTH=1200
SCREENSHOT_VIEWPORT_HEIGHT=800
SCREENSHOT_DEVICE_SCALE_FACTOR=3

# Image comparison settings
COMPARISON_THRESHOLD=0.1
COMPARISON_INCLUDE_AA=false

# Debugging options
DEBUG_MODE=false
VERBOSE_LOGGING=false

# Temporary file cleanup
AUTO_CLEANUP_TEMP_FILES=true
TEMP_FILE_RETENTION_HOURS=24
