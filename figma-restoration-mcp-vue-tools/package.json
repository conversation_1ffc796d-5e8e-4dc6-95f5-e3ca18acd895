{"name": "figma-restoration-mcp-vue-tools", "version": "1.3.1", "description": "Professional Figma Component Restoration Kit - MCP tools with snapDOM-powered high-quality screenshots, intelligent shadow detection, and smart debugging for Vue component restoration. Includes figma_compare and snapdom_screenshot tools.", "type": "module", "main": "src/server.js", "bin": {"figma-restoration-mcp": "./bin/cli.js"}, "scripts": {"start": "node src/server.js", "mcp": "node --watch src/server.js", "dev": "vite", "build": "echo \"Build completed - no build step required\" && exit 0", "preview": "vite preview", "test": "node test-installation.js", "clean": "node test-clean-tools.mjs", "install-kit": "chmod +x scripts/install.sh && ./scripts/install.sh", "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh", "prepublishOnly": "echo 'Skipping pre-publish checks for release'", "postinstall": "node scripts/postinstall.js", "publish:npm": "./scripts/publish.sh", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major"}, "config": {"puppeteer": {"skipDownload": true, "skipChromiumDownload": true}}, "puppeteer": {"skipDownload": true}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "@zumer/snapdom": "^1.9.5", "chalk": "^5.3.0", "element-plus": "^2.10.4", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "puppeteer": "^21.0.0", "sharp": "^0.34.3", "svgo": "^4.0.0", "vue-router": "^4.5.1"}, "bundledDependencies": ["puppeteer"], "devDependencies": {"@types/node": "^20.0.0", "nodemon": "^3.0.0", "typescript": "^5.0.0"}, "keywords": ["figma", "restoration", "vue", "component", "mcp", "screenshot", "snapdom", "design-to-code", "ui-testing", "visual-regression", "shadow-detection", "image-comparison", "svg-optimization", "svgo"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tianmuji/figma-restoration-mcp-vue-tools.git"}, "homepage": "https://github.com/tianmuji/figma-restoration-mcp-vue-tools#readme", "bugs": {"url": "https://github.com/tianmuji/figma-restoration-mcp-vue-tools/issues"}, "files": ["src/", "bin/", "config/", "docs/", "scripts/", "examples/", "assets/", ".augment/", ".cursor/", "README.md", "LICENSE", "package.json", "vite.config.js"], "engines": {"node": ">=18.0.0"}, "bundleDependencies": ["puppeteer"]}