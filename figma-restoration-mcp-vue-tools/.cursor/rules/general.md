---
type: "always_apply"
description: "figma-restoration-mcp-vue-tools package rules"
---
# Figma Restoration MCP Vue Tools - Cursor IDE Rules

## Project Context
This is the `figma-restoration-mcp-vue-tools` npm package - a professional Figma component restoration toolkit with MCP (Model Context Protocol) integration.

## Core Guidelines for Cursor IDE Users
- Use English for documentation and code comments (for international users)
- Focus on Vue 3 Composition API syntax
- Maintain high code quality and professional standards
- Provide comprehensive examples and documentation

## Package Structure
- `src/` - Source code including MCP tools and Vue components
- `bin/` - CLI executable for package users
- `docs/` - Documentation and guides
- `config/` - Configuration templates for users
- `examples/` - Usage examples and sample components

## Development Principles
- Write clean, maintainable, and well-documented code
- Follow Vue 3 best practices and modern JavaScript standards
- Ensure compatibility with MCP protocol requirements
- Provide clear installation and usage instructions

## Cursor IDE Integration
- This package includes MCP tools that integrate with Cursor IDE
- Use the provided CLI commands for project initialization
- Follow the configuration templates in the `config/` directory