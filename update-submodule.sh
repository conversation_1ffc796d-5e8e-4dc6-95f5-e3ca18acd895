#!/bin/bash
# Semi-automated submodule update script
# Usage: ./update-submodule.sh [commit-message]

SUBMODULE_NAME="figma-restoration-mcp-vue-tools"
MAIN_PROJECT_DIR="/Users/<USER>/Documents/study/11111"

# Function to update submodule pointer
update_submodule_pointer() {
    local custom_message="$1"
    
    echo "🔍 Checking submodule status..."
    cd "$MAIN_PROJECT_DIR"
    
    # Check if there are submodule changes
    if git status --porcelain | grep -q " M $SUBMODULE_NAME"; then
        echo "✅ Detected submodule changes"
        
        # Get the latest commit message from submodule
        SUBMODULE_COMMIT_MSG=$(cd "$SUBMODULE_NAME" && git log -1 --pretty=format:"%s")
        echo "📝 Latest submodule commit: $SUBMODULE_COMMIT_MSG"
        
        # Use custom message or generate one
        if [ -n "$custom_message" ]; then
            COMMIT_MSG="$custom_message"
        else
            COMMIT_MSG="chore: update $SUBMODULE_NAME submodule

Latest change: $SUBMODULE_COMMIT_MSG"
        fi
        
        echo "🚀 Updating submodule pointer..."
        git add "$SUBMODULE_NAME"
        git commit -m "$COMMIT_MSG"
        
        echo "✅ Submodule pointer updated successfully!"
        echo "📋 You can now run: git push origin main"
        
    else
        echo "ℹ️  No submodule changes detected"
        echo "Current git status:"
        git status --short
    fi
}

# Function for quick commit in submodule + update main project
submodule_commit_and_update() {
    local commit_message="$1"
    
    if [ -z "$commit_message" ]; then
        echo "❌ Usage: submodule_commit_and_update 'commit message'"
        return 1
    fi
    
    echo "📝 Committing changes in submodule..."
    cd "$MAIN_PROJECT_DIR/$SUBMODULE_NAME"
    
    # Check if there are changes to commit
    if [ -n "$(git status --porcelain)" ]; then
        git add .
        git commit -m "$commit_message"
        echo "✅ Submodule commit successful"
        
        # Update main project
        echo "🔄 Updating main project..."
        update_submodule_pointer
    else
        echo "ℹ️  No changes to commit in submodule"
    fi
}

# Main script logic
case "$1" in
    "commit")
        shift
        submodule_commit_and_update "$*"
        ;;
    "update")
        shift
        update_submodule_pointer "$*"
        ;;
    *)
        echo "🛠️  Figma Component Submodule Helper"
        echo ""
        echo "Usage:"
        echo "  ./update-submodule.sh update [custom-message]    - Update submodule pointer only"
        echo "  ./update-submodule.sh commit 'message'           - Commit in submodule + update main"
        echo ""
        echo "Examples:"
        echo "  ./update-submodule.sh update"
        echo "  ./update-submodule.sh commit 'feat: add NewComponent restoration'"
        echo ""
        update_submodule_pointer
        ;;
esac
