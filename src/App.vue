<script setup lang="ts">
import { ref } from 'vue'
import HelloWorld from './components/HelloWorld.vue'

// 演示如何使用Figma恢复的组件
// import { DocumentSortMenu } from './components/FigmaComponents'
// import type { SortOption } from './components/FigmaComponents'

const count = ref(0)
</script>

<template>
  <div>
    <a href="https://vitejs.dev" target="_blank">
      <img src="/vite.svg" class="logo" alt="Vite logo" />
    </a>
    <a href="https://vuejs.org/" target="_blank">
      <img src="./assets/vue.svg" class="logo vue" alt="Vue logo" />
    </a>
  </div>
  
  <HelloWorld msg="Vite + Vue" />
  
  <div class="card">
    <button type="button" @click="count++">count is {{ count }}</button>
    <p>
      Edit
      <code>src/App.vue</code> to test HMR
    </p>
  </div>

  <!-- Figma组件使用示例 (取消注释以使用) -->
  <!--
  <div class="figma-components-demo">
    <h2>🎨 Figma恢复的组件</h2>
    <DocumentSortMenu 
      :selected-option="'modifiedTimeDesc'"
      @select="(option) => console.log('选择了:', option)"
    />
  </div>
  -->

  <p class="read-the-docs">
    Click on the Vite and Vue logos to learn more
  </p>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}

.figma-components-demo {
  margin: 2rem 0;
  padding: 2rem;
  border: 2px dashed #42b883;
  border-radius: 8px;
  background: #f9f9f9;
}

.figma-components-demo h2 {
  color: #42b883;
  margin-bottom: 1rem;
}
</style>
