/**
 * 🎨 Figma组件代理
 * 
 * 这个文件是主项目和Figma子模块之间的桥梁
 * 主项目通过这里导入Figma恢复的组件，就像使用本地组件一样
 * 
 * 使用方法：
 * import { DocumentSortMenu } from './components/FigmaComponents'
 * import type { SortOption } from './components/FigmaComponents'
 */

// 当需要使用Figma恢复的组件时，取消注释以下代码：

/*
// 从子模块导入组件（只有这个文件知道子模块结构）
import DocumentSortMenuComponent from '../figma-restoration-mcp-vue-tools/src/components/DocumentSortMenu/index.vue'

// 重新导出为干净的名称（主项目看到的是本地组件）
export const DocumentSortMenu = DocumentSortMenuComponent

// 导出类型（主项目获得干净的类型导入）
export type { SortOption, DocumentSortMenuProps } from '../figma-restoration-mcp-vue-tools/src/components/DocumentSortMenu/index.vue'

// 默认导出，方便使用
export default {
  DocumentSortMenu
}
*/

// 临时导出（在实际使用Figma组件前）
export const DocumentSortMenu = null
export type SortOption = string
export type DocumentSortMenuProps = Record<string, any>

export default {
  DocumentSortMenu
}
