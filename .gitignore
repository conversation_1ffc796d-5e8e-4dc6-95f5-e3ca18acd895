# 🎯 Vue 3 + TypeScript + Vite Project .gitignore
# Comprehensive configuration for clean Git history and submodule compatibility

# ===================================
# 📦 Dependencies & Package Managers
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# Package manager lock files (keep package-lock.json, ignore others if not used)
# yarn.lock
# pnpm-lock.yaml

# ===================================
# 🏗️ Build Outputs & Distribution
# ===================================
dist/
dist-ssr/
build/
out/
.output/
.nuxt/
.next/
.vercel/
.netlify/

# Vite build outputs
*.local

# ===================================
# 🔧 Development & IDE Files
# ===================================

# VS Code
.vscode/
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# WebStorm / IntelliJ IDEA
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*.swn
.vim/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# 🌍 Environment & Configuration
# ===================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Local configuration overrides
config.local.js
config.local.ts
vite.config.local.ts
*.local.json

# ===================================
# 🧪 Testing & Development Outputs
# ===================================

# Test results
coverage/
*.lcov
.nyc_output/
test-results/
playwright-report/
test-results.xml

# Screenshots and visual testing
screenshots/
visual-regression/
percy/
chromatic-build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# ===================================
# 💾 Cache & Temporary Files
# ===================================

# TypeScript cache
*.tsbuildinfo
.tsbuildinfo

# Vite cache
.vite/
vite.config.*.timestamp-*

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# Node.js cache
.npm/
.node_repl_history

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# ===================================
# 🖥️ Operating System Files
# ===================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# 🔒 Security & Sensitive Data
# ===================================

# API keys and secrets
.env.secret
secrets.json
*.pem
*.key
*.crt
*.p12
*.pfx

# ===================================
# 📱 Mobile Development (if applicable)
# ===================================

# Capacitor
android/
ios/
.capacitor/

# ===================================
# 🎨 Figma Integration Specific
# ===================================

# Figma component testing outputs (main project level)
figma-screenshots/
figma-exports/
figma-temp/

# MCP tools results (if accidentally created in main project)
mcp-vue-tools/

# ===================================
# 🚫 Submodule Integrity Protection
# ===================================

# DO NOT ignore the submodule directory itself
# figma-restoration-mcp-vue-tools/ should be tracked by Git

# DO NOT ignore .gitmodules file
# !.gitmodules

# ===================================
# 📊 Analytics & Monitoring
# ===================================

# Bundle analyzer outputs
bundle-analyzer-report.html
stats.json

# Performance monitoring
.lighthouse/
lighthouse-report.html

# ===================================
# 🔄 CI/CD & Deployment
# ===================================

# Deployment artifacts
deploy/
deployment/
.deploy/

# Docker
.dockerignore
docker-compose.override.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# ===================================
# 📝 Documentation Generation
# ===================================

# Auto-generated docs
docs/dist/
.vuepress/dist/
.docusaurus/

# ===================================
# 🎯 Project Specific
# ===================================

# Temporary development files
temp/
tmp/
.temp/
.tmp/

# Backup files
*.backup
*.bak
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# ===================================
# ✅ Explicitly Include Important Files
# ===================================

# Ensure these are never ignored
!.gitkeep
!.gitmodules
!.github/
!public/
!src/
!*.md
!package.json
!tsconfig*.json
!vite.config.ts
!index.html
