#!/usr/bin/env node

/**
 * 测试路径修改是否正确工作
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testPathChanges() {
  console.log('🧪 测试 Submodule 路径修改...\n');

  // 测试1: 检查 src/results 目录是否存在
  console.log('📁 测试1: 检查 src/results 目录');
  const srcResultsPath = path.join(__dirname, 'src', 'results');
  try {
    await fs.access(srcResultsPath);
    console.log('✅ src/results 目录存在');
  } catch (error) {
    console.log('❌ src/results 目录不存在，正在创建...');
    await fs.mkdir(srcResultsPath, { recursive: true });
    console.log('✅ src/results 目录已创建');
  }

  // 测试2: 创建测试组件目录和文件
  console.log('\n📝 测试2: 创建测试文件');
  const testComponentName = 'TestComponent';
  const testComponentPath = path.join(srcResultsPath, testComponentName);
  
  try {
    await fs.mkdir(testComponentPath, { recursive: true });
    
    // 创建测试文件
    const testFiles = [
      'actual.png',
      'expected.png', 
      'diff.png',
      'figma-analysis-report.json'
    ];
    
    for (const fileName of testFiles) {
      const filePath = path.join(testComponentPath, fileName);
      if (fileName.endsWith('.json')) {
        const testReport = {
          componentName: testComponentName,
          timestamp: new Date().toISOString(),
          summary: {
            matchPercentage: 98.5,
            pixelMatch: "1234/56789",
            files: {
              component: `/mcp-vue-tools/src/components/${testComponentName}/index.vue`,
              screenshot: `/src/results/${testComponentName}/actual.png`,
              expected: `/src/results/${testComponentName}/expected.png`,
              diff: `/src/results/${testComponentName}/diff.png`
            }
          }
        };
        await fs.writeFile(filePath, JSON.stringify(testReport, null, 2));
      } else {
        // 创建空的PNG文件占位符
        await fs.writeFile(filePath, Buffer.from('test-placeholder'));
      }
      console.log(`✅ 创建测试文件: ${fileName}`);
    }
    
  } catch (error) {
    console.log(`❌ 创建测试文件失败: ${error.message}`);
  }

  // 测试3: 验证路径配置
  console.log('\n🔧 测试3: 验证路径配置');
  try {
    // 动态导入路径配置模块
    const pathConfigPath = path.join(__dirname, 'figma-restoration-mcp-vue-tools', 'src', 'utils', 'path-config.js');
    const { getResultsPath } = await import(pathConfigPath);
    
    const resultsPath = getResultsPath(testComponentName);
    console.log(`📍 getResultsPath 返回: ${resultsPath}`);
    
    // 检查路径是否指向正确的位置
    if (resultsPath.includes('src/results')) {
      console.log('✅ 路径配置正确，指向 src/results');
    } else {
      console.log('❌ 路径配置错误，未指向 src/results');
    }
    
  } catch (error) {
    console.log(`❌ 路径配置测试失败: ${error.message}`);
  }

  // 测试4: 检查修改的文件
  console.log('\n📋 测试4: 检查修改的文件');
  const modifiedFiles = [
    'figma-restoration-mcp-vue-tools/src/tools/figma-compare.js',
    'figma-restoration-mcp-vue-tools/scripts/smart-compare.js',
    'figma-restoration-mcp-vue-tools/scripts/generate-diff.js',
    'figma-restoration-mcp-vue-tools/scripts/generate-reports.js',
    'figma-restoration-mcp-vue-tools/src/utils/path-config.js',
    'figma-restoration-mcp-vue-tools/src/views/ComparisonReport.vue',
    'figma-restoration-mcp-vue-tools/src/views/Home.vue'
  ];

  for (const filePath of modifiedFiles) {
    const fullPath = path.join(__dirname, filePath);
    try {
      await fs.access(fullPath);
      const content = await fs.readFile(fullPath, 'utf-8');
      
      // 检查是否包含新的路径引用
      if (content.includes('src/results') || content.includes('../../src/results')) {
        console.log(`✅ ${filePath} - 包含新路径引用`);
      } else if (filePath.includes('Home.vue')) {
        // Home.vue 主要是样式修改，检查是否有新的样式类
        if (content.includes('components-container') && content.includes('view-toggle')) {
          console.log(`✅ ${filePath} - 包含新的样式优化`);
        } else {
          console.log(`⚠️  ${filePath} - 样式修改可能不完整`);
        }
      } else {
        console.log(`⚠️  ${filePath} - 未找到新路径引用`);
      }
    } catch (error) {
      console.log(`❌ ${filePath} - 文件不存在或无法访问`);
    }
  }

  console.log('\n🎉 测试完成！');
  console.log('\n📖 使用说明:');
  console.log('1. 访问 http://localhost:83/ 查看优化后的组件列表界面');
  console.log('2. 图片对比结果现在存储在 src/results/ 目录');
  console.log('3. 可以使用网格视图和列表视图切换');
  console.log('4. 组件卡片显示类型、状态和标签信息');
}

// 运行测试
testPathChanges().catch(console.error);
